# Woo Cash Manager

A comprehensive WordPress plugin that integrates with WooCommerce to track your store's cash flow by calculating income from orders and managing manually added expenses.

## Features

### 🎯 Core Functionality
- **Cash Balance Calculation**: Automatically calculates current cash balance (Income - Expenses)
- **WooCommerce Integration**: Pulls income data from completed and processing orders
- **Expense Management**: Add, edit, and delete business expenses with detailed categorization
- **Visual Dashboard**: Interactive charts showing income vs expenses over time

### 📊 Dashboard Features
- Current cash balance with positive/negative indicators
- Total income from WooCommerce orders
- Total expenses from manual entries
- Monthly, quarterly, and yearly chart views using Chart.js
- Recent expenses overview
- Quick action buttons for common tasks

### 💰 Expense Management
- **Add Expenses**: Simple form with title, amount, category, date, and notes
- **Expense Categories**: Organize expenses with custom categories
- **Expense Templates**: Quick templates for common business expenses
- **Edit/Delete**: Full CRUD operations with confirmation dialogs
- **Filtering**: Search and filter expenses by category, date range, and keywords
- **Pagination**: Efficient handling of large expense lists

### 🔒 Security Features
- WordPress nonce protection for all forms
- Capability checks (`manage_woocommerce` required)
- Input sanitization and validation
- AJAX security with nonce verification

### ⚡ WooCommerce HPOS Compatibility
- **Full HPOS Support**: Compatible with WooCommerce High-Performance Order Storage
- **Automatic Detection**: Automatically detects and uses HPOS when enabled
- **Fallback Support**: Falls back to traditional order storage for legacy stores
- **Performance Optimized**: Uses direct database queries for better performance with HPOS

## Installation

1. **Upload the Plugin**:
   - Upload the `woo-cash-manager` folder to `/wp-content/plugins/`
   - Or install via WordPress admin: Plugins > Add New > Upload Plugin

2. **Activate the Plugin**:
   - Go to Plugins > Installed Plugins
   - Find "Woo Cash Manager" and click "Activate"

3. **Requirements Check**:
   - The plugin will automatically check if WooCommerce is active
   - If WooCommerce is not found, you'll see an admin notice

## Usage

### Accessing the Plugin
1. Navigate to **WooCommerce > Cash Manager** in your WordPress admin
2. You'll see the main dashboard with your current cash balance

### Adding Expenses
1. Click "Add Expense" from the dashboard or expenses page
2. Fill in the required fields:
   - **Title**: Descriptive name for the expense
   - **Amount**: Cost of the expense
   - **Category**: Organize expenses (suggestions provided)
   - **Date**: When the expense occurred
   - **Note**: Optional additional details

3. Use quick category buttons or expense templates for faster entry
4. Click "Add Expense" to save

### Managing Expenses
1. Go to **WooCommerce > Expenses** to view all expenses
2. Use filters to search by category, date range, or keywords
3. Click "Edit" to modify an expense
4. Click "Delete" to remove an expense (with confirmation)

### Understanding the Dashboard
- **Current Cash Balance**: Income minus expenses (green = positive, red = negative)
- **Total Income**: Sum of all completed and processing WooCommerce orders
- **Total Expenses**: Sum of all manually added expenses
- **Chart**: Visual representation of income vs expenses over time

## File Structure

```
woo-cash-manager/
├── woo-cash-manager.php          # Main plugin file
├── includes/
│   ├── class-wcm-admin.php       # Admin functionality
│   └── class-wcm-ajax.php        # AJAX handlers
├── templates/
│   ├── dashboard.php             # Main dashboard page
│   ├── expenses-list.php         # Expenses listing page
│   ├── add-expense.php           # Add expense form
│   └── edit-expense.php          # Edit expense form
├── assets/
│   ├── css/
│   │   └── admin.css             # Admin styles
│   └── js/
│       └── admin.js              # Admin JavaScript
└── README.md                     # This file
```

## Database Schema

The plugin creates a custom table `wp_wc_cash_expenses` with the following structure:

```sql
CREATE TABLE wp_wc_cash_expenses (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    title varchar(255) NOT NULL,
    amount decimal(10,2) NOT NULL,
    category varchar(100) NOT NULL,
    note text,
    expense_date date NOT NULL,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY expense_date (expense_date),
    KEY category (category)
);
```

## Hooks and Filters

### Actions
- `wcm_expense_added` - Fired when a new expense is added
- `wcm_expense_updated` - Fired when an expense is updated
- `wcm_expense_deleted` - Fired when an expense is deleted

### Filters
- `wcm_expense_categories` - Filter available expense categories
- `wcm_dashboard_cards` - Filter dashboard card data
- `wcm_chart_data` - Filter chart data before display

## AJAX Endpoints

- `wcm_delete_expense` - Delete an expense via AJAX
- `wcm_get_chart_data` - Get chart data for different periods
- `wcm_refresh_dashboard` - Refresh dashboard data

## Requirements

- **WordPress**: 5.0 or higher
- **PHP**: 7.4 or higher
- **WooCommerce**: 5.0 or higher (HPOS compatible)
- **User Capability**: `manage_woocommerce`

### WooCommerce HPOS Support
This plugin is fully compatible with WooCommerce's High-Performance Order Storage (HPOS). It automatically detects whether HPOS is enabled and uses the appropriate data access methods for optimal performance.

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Responsive Design

The plugin is fully responsive and works on:
- Desktop computers
- Tablets
- Mobile phones

## Troubleshooting

### Common Issues

1. **Plugin won't activate**:
   - Ensure WooCommerce is installed and active
   - Check PHP version (7.4+ required)

2. **Charts not displaying**:
   - Check browser console for JavaScript errors
   - Ensure Chart.js is loading properly

3. **Expenses not saving**:
   - Check form validation errors
   - Verify database permissions

4. **Permission errors**:
   - Ensure user has `manage_woocommerce` capability
   - Check WordPress user roles

### Debug Mode

To enable debug mode, add this to your `wp-config.php`:

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Support

For support and feature requests, please contact the plugin developer or check the plugin documentation.

## License

This plugin is licensed under the GPL v2 or later.

## Changelog

### Version 1.0.0
- Initial release
- Dashboard with cash balance calculation
- Expense management (CRUD operations)
- Chart.js integration for visual reports
- Responsive design
- Security features and validation
