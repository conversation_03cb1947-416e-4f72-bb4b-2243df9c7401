Woo Cash Manager Plugin Information
=====================================

Plugin Name: Woo Cash Manager
Version: 1.0.0
Author: Your Name
Description: A comprehensive cash management plugin for WooCommerce

INSTALLATION INSTRUCTIONS:
==========================

1. Prerequisites:
   - WordPress 5.0 or higher
   - PHP 7.4 or higher
   - WooCommerce 5.0 or higher

2. Installation Steps:
   - Upload the entire 'woo-cash-manager' folder to /wp-content/plugins/
   - Activate the plugin through the 'Plugins' menu in WordPress
   - Navigate to WooCommerce > Cash Manager to start using the plugin

3. First Time Setup:
   - The plugin will automatically create the necessary database table
   - No additional configuration is required
   - Start by adding your first expense or viewing the dashboard

FEATURES OVERVIEW:
==================

✅ Dashboard with real-time cash balance calculation
✅ WooCommerce order integration (completed & processing orders)
✅ Expense management (add, edit, delete)
✅ Category-based expense organization
✅ Interactive charts with Chart.js
✅ Responsive design for all devices
✅ Search and filter functionality
✅ Secure with WordPress nonces and capability checks
✅ AJAX-powered for smooth user experience
✅ WooCommerce HPOS (High-Performance Order Storage) compatible

SECURITY FEATURES:
==================

- WordPress nonce protection on all forms
- User capability checks (manage_woocommerce required)
- Input sanitization and validation
- SQL injection prevention with prepared statements
- XSS protection with proper escaping

TECHNICAL DETAILS:
==================

Database Table: wp_wc_cash_expenses
- Stores all manually added expenses
- Indexed for optimal performance
- Includes audit trail (created_at, updated_at)

File Structure:
- Main plugin file: woo-cash-manager.php
- Admin functionality: includes/class-wcm-admin.php
- AJAX handlers: includes/class-wcm-ajax.php
- Templates: templates/ directory
- Assets: assets/css/ and assets/js/

CUSTOMIZATION:
==============

The plugin includes several hooks for customization:

Actions:
- wcm_expense_added
- wcm_expense_updated
- wcm_expense_deleted

Filters:
- wcm_expense_categories
- wcm_dashboard_cards
- wcm_chart_data

SUPPORT:
========

For support, please check:
1. Plugin documentation (README.md)
2. WordPress debug logs
3. Browser console for JavaScript errors

UNINSTALLATION:
===============

The plugin includes an uninstall.php file that will:
- Remove the custom database table
- Delete all plugin options
- Clean up transients and cached data

This ensures a clean removal if you decide to uninstall the plugin.

COMPATIBILITY:
==============

Tested with:
- WordPress 5.0 - 6.3
- WooCommerce 5.0 - 8.0 (including HPOS)
- PHP 7.4 - 8.2
- Modern browsers (Chrome, Firefox, Safari, Edge)

WooCommerce HPOS Support:
- Fully compatible with High-Performance Order Storage
- Automatic detection and fallback support
- Optimized database queries for better performance

CHANGELOG:
==========

Version 1.0.0 (Initial Release):
- Dashboard with cash balance calculation
- Expense CRUD operations
- Chart.js integration
- Responsive design
- Security implementation
- WooCommerce integration
