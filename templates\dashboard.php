<?php
/**
 * Dashboard template for Woo Cash Manager
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Handle messages
$message = isset($_GET['message']) ? sanitize_text_field($_GET['message']) : '';
?>

<div class="wrap wcm-dashboard">
    <h1 class="wp-heading-inline"><?php _e('Cash Manager Dashboard', 'woo-cash-manager'); ?></h1>
    <button type="button" class="page-title-action" id="wcm-add-expense-btn"><?php _e('Add Expense', 'woo-cash-manager'); ?></button>
    <hr class="wp-header-end">
    
    <?php if ($message): ?>
        <div class="notice notice-success is-dismissible">
            <p>
                <?php
                switch ($message) {
                    case 'added':
                        _e('Expense added successfully.', 'woo-cash-manager');
                        break;
                    case 'updated':
                        _e('Expense updated successfully.', 'woo-cash-manager');
                        break;
                    case 'deleted':
                        _e('Expense deleted successfully.', 'woo-cash-manager');
                        break;
                }
                ?>
            </p>
        </div>
    <?php endif; ?>
    
    <!-- Cash Balance Cards -->
    <div class="wcm-cards-container" style="animation-delay: 0.1s;">
        <div class="wcm-card wcm-card-balance <?php echo $cash_data['balance'] >= 0 ? 'positive' : 'negative'; ?>">
            <div class="wcm-card-header">
                <h3><span class="dashicons dashicons-chart-line"></span><?php _e('Current Cash Balance', 'woo-cash-manager'); ?></h3>
                <span class="wcm-refresh-btn" id="wcm-refresh-dashboard" title="<?php _e('Refresh Data', 'woo-cash-manager'); ?>">
                    <span class="dashicons dashicons-update"></span>
                </span>
            </div>
            <div class="wcm-card-content">
                <div class="wcm-amount" id="wcm-balance">
                    <?php echo wc_price($cash_data['balance']); ?>
                </div>
                <div class="wcm-status">
                    <?php if ($cash_data['balance'] >= 0): ?>
                        <span class="wcm-status-positive"><?php _e('Positive Balance', 'woo-cash-manager'); ?></span>
                    <?php else: ?>
                        <span class="wcm-status-negative"><?php _e('Negative Balance', 'woo-cash-manager'); ?></span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="wcm-card wcm-card-income">
            <div class="wcm-card-header">
                <h3><span class="dashicons dashicons-arrow-up-alt"></span><?php _e('Total Income', 'woo-cash-manager'); ?></h3>
                <span class="wcm-info-icon" title="<?php _e('From completed and processing WooCommerce orders', 'woo-cash-manager'); ?>">
                    <span class="dashicons dashicons-info"></span>
                </span>
            </div>
            <div class="wcm-card-content">
                <div class="wcm-amount" id="wcm-income">
                    <?php echo wc_price($cash_data['income']); ?>
                </div>
                <div class="wcm-subtitle">
                    <?php _e('WooCommerce Orders', 'woo-cash-manager'); ?>
                </div>
            </div>
        </div>
        
        <div class="wcm-card wcm-card-expenses">
            <div class="wcm-card-header">
                <h3><span class="dashicons dashicons-arrow-down-alt"></span><?php _e('Total Expenses', 'woo-cash-manager'); ?></h3>
                <button type="button" class="wcm-view-link wcm-view-all-expenses" data-target="wcm-all-expenses">
                    <?php _e('View All', 'woo-cash-manager'); ?>
                </button>
            </div>
            <div class="wcm-card-content">
                <div class="wcm-amount" id="wcm-expenses">
                    <?php echo wc_price($cash_data['expenses']); ?>
                </div>
                <div class="wcm-subtitle">
                    <?php _e('Manual Entries', 'woo-cash-manager'); ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Chart Section -->
    <div class="wcm-chart-section">
        <div class="wcm-chart-header">
            <h2><?php _e('Income vs Expenses Chart', 'woo-cash-manager'); ?></h2>
            <div class="wcm-chart-controls">
                <select id="wcm-chart-period">
                    <option value="monthly"><?php _e('Monthly (12 months)', 'woo-cash-manager'); ?></option>
                    <option value="quarterly"><?php _e('Quarterly (8 quarters)', 'woo-cash-manager'); ?></option>
                    <option value="yearly"><?php _e('Yearly (5 years)', 'woo-cash-manager'); ?></option>
                </select>
                <button type="button" class="button" id="wcm-update-chart"><?php _e('Update Chart', 'woo-cash-manager'); ?></button>
            </div>
        </div>
        <div class="wcm-chart-container">
            <canvas id="wcm-chart" width="400" height="200"></canvas>
        </div>
    </div>
    
    
    <!-- HPOS Status (for debugging) -->
    <?php if (defined('WP_DEBUG') && WP_DEBUG): ?>
        <div class="wcm-debug-info" style="background: #f0f0f1; padding: 10px; margin: 20px 0; border-radius: 4px; font-size: 12px;">
            <strong><?php _e('Debug Info:', 'woo-cash-manager'); ?></strong>
            <?php if (function_exists('wcm_is_hpos_enabled')): ?>
                HPOS Status: <?php echo wcm_is_hpos_enabled() ? 'Enabled ✅' : 'Disabled ❌'; ?>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <!-- All Expenses -->
    <div class="wcm-all-expenses" id="wcm-all-expenses">
        <div class="wcm-section-header">
            <h2><span class="dashicons dashicons-chart-line"></span><?php _e('All Expenses', 'woo-cash-manager'); ?></h2>
            <div class="wcm-expenses-actions">
                <button type="button" class="button button-primary wcm-add-expense-trigger">
                    <span class="dashicons dashicons-plus-alt"></span>
                    <?php _e('Add New Expense', 'woo-cash-manager'); ?>
                </button>
                <button type="button" class="button button-secondary" id="wcm-refresh-expenses">
                    <span class="dashicons dashicons-update"></span>
                    <?php _e('Refresh', 'woo-cash-manager'); ?>
                </button>
            </div>
        </div>

        <!-- Expenses Filters -->
        <div class="wcm-expenses-filters">
            <div class="wcm-filters-row">
                <div class="wcm-filter-group">
                    <label for="wcm-search-expenses"><?php _e('Search:', 'woo-cash-manager'); ?></label>
                    <input type="text" id="wcm-search-expenses" class="wcm-filter-input"
                           placeholder="<?php _e('Search by title, note, or category...', 'woo-cash-manager'); ?>"
                           value="<?php echo isset($_GET['search']) ? esc_attr($_GET['search']) : ''; ?>">
                </div>

                <div class="wcm-filter-group">
                    <label for="wcm-filter-category"><?php _e('Category:', 'woo-cash-manager'); ?></label>
                    <select id="wcm-filter-category" class="wcm-filter-select">
                        <option value=""><?php _e('All Categories', 'woo-cash-manager'); ?></option>
                        <?php if (!empty($common_categories)): ?>
                            <?php foreach ($common_categories as $category): ?>
                                <option value="<?php echo esc_attr($category); ?>"
                                        <?php selected(isset($_GET['category']) ? $_GET['category'] : '', $category); ?>>
                                    <?php echo esc_html($category); ?>
                                </option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                </div>

                <div class="wcm-filter-group">
                    <label for="wcm-filter-date-from"><?php _e('Date From:', 'woo-cash-manager'); ?></label>
                    <input type="date" id="wcm-filter-date-from" class="wcm-filter-input"
                           value="<?php echo isset($_GET['date_from']) ? esc_attr($_GET['date_from']) : ''; ?>">
                </div>

                <div class="wcm-filter-group">
                    <label for="wcm-filter-date-to"><?php _e('Date To:', 'woo-cash-manager'); ?></label>
                    <input type="date" id="wcm-filter-date-to" class="wcm-filter-input"
                           value="<?php echo isset($_GET['date_to']) ? esc_attr($_GET['date_to']) : ''; ?>">
                </div>

                <div class="wcm-filter-actions">
                    <button type="button" class="button button-primary" id="wcm-apply-filters">
                        <span class="dashicons dashicons-search"></span>
                        <?php _e('Filter', 'woo-cash-manager'); ?>
                    </button>
                    <button type="button" class="button button-secondary" id="wcm-clear-filters">
                        <span class="dashicons dashicons-dismiss"></span>
                        <?php _e('Clear', 'woo-cash-manager'); ?>
                    </button>
                </div>
            </div>
        </div>

        <!-- Expenses Summary -->
        <div class="wcm-expenses-summary">
            <div class="wcm-summary-grid">
                <div class="wcm-summary-item">
                    <div class="wcm-summary-icon">
                        <span class="dashicons dashicons-money-alt"></span>
                    </div>
                    <div class="wcm-summary-content">
                        <span class="wcm-summary-label"><?php _e('Total Expenses', 'woo-cash-manager'); ?></span>
                        <span class="wcm-summary-value"><?php echo wc_price($cash_data['expenses']); ?></span>
                    </div>
                </div>
                <div class="wcm-summary-item">
                    <div class="wcm-summary-icon">
                        <span class="dashicons dashicons-list-view"></span>
                    </div>
                    <div class="wcm-summary-content">
                        <span class="wcm-summary-label"><?php _e('Total Entries', 'woo-cash-manager'); ?></span>
                        <span class="wcm-summary-value"><?php echo number_format($total_expenses); ?></span>
                    </div>
                </div>
                <div class="wcm-summary-item">
                    <div class="wcm-summary-icon">
                        <span class="dashicons dashicons-category"></span>
                    </div>
                    <div class="wcm-summary-content">
                        <span class="wcm-summary-label"><?php _e('Categories', 'woo-cash-manager'); ?></span>
                        <span class="wcm-summary-value"><?php echo count($common_categories); ?></span>
                    </div>
                </div>
                <div class="wcm-summary-item">
                    <div class="wcm-summary-icon">
                        <span class="dashicons dashicons-calendar-alt"></span>
                    </div>
                    <div class="wcm-summary-content">
                        <span class="wcm-summary-label"><?php _e('This Month', 'woo-cash-manager'); ?></span>
                        <span class="wcm-summary-value"><?php
                            // Get current month expenses
                            global $wpdb;
                            $table_name = $wpdb->prefix . WCM_TABLE_NAME;
                            $start_date = date('Y-m-01');
                            $end_date = date('Y-m-t');
                            $current_month_expenses = $wpdb->get_var($wpdb->prepare(
                                "SELECT SUM(amount) FROM $table_name WHERE expense_date BETWEEN %s AND %s",
                                $start_date,
                                $end_date
                            ));
                            echo wc_price($current_month_expenses ? $current_month_expenses : 0);
                        ?></span>
                    </div>
                </div>
            </div>
        </div>
        
        <?php if ($expenses): ?>
            <div class="wcm-expenses-table-container">
                <div class="wcm-table-header">
                    <div class="wcm-table-info">
                        <span class="wcm-showing-results">
                            <?php printf(__('Showing %d of %d expenses', 'woo-cash-manager'), count($expenses), $total_expenses); ?>
                        </span>
                    </div>
                    <div class="wcm-table-actions">
                        <button type="button" class="button button-small" id="wcm-export-expenses">
                            <span class="dashicons dashicons-download"></span>
                            <?php _e('Export', 'woo-cash-manager'); ?>
                        </button>
                    </div>
                </div>

                <div class="wcm-table-wrapper">
                    <table class="wp-list-table widefat fixed striped wcm-expenses-table">
                        <thead>
                            <tr>
                                <th class="wcm-col-title sortable" data-sort="title">
                                    <?php _e('Title', 'woo-cash-manager'); ?>
                                    <span class="dashicons dashicons-sort"></span>
                                </th>
                                <th class="wcm-col-amount sortable" data-sort="amount">
                                    <?php _e('Amount', 'woo-cash-manager'); ?>
                                    <span class="dashicons dashicons-sort"></span>
                                </th>
                                <th class="wcm-col-category sortable" data-sort="category">
                                    <?php _e('Category', 'woo-cash-manager'); ?>
                                    <span class="dashicons dashicons-sort"></span>
                                </th>
                                <th class="wcm-col-date sortable" data-sort="expense_date">
                                    <?php _e('Date', 'woo-cash-manager'); ?>
                                    <span class="dashicons dashicons-sort"></span>
                                </th>
                                <th class="wcm-col-actions"><?php _e('Actions', 'woo-cash-manager'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($expenses as $expense): ?>
                                <tr data-expense-id="<?php echo $expense->id; ?>" class="wcm-expense-row">
                                    <td class="wcm-col-title">
                                        <div class="wcm-expense-title">
                                            <strong><?php echo esc_html($expense->title); ?></strong>
                                            <?php if ($expense->note): ?>
                                                <div class="wcm-expense-note">
                                                    <span class="dashicons dashicons-admin-comments"></span>
                                                    <?php echo esc_html($expense->note); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="wcm-col-amount">
                                        <span class="wcm-amount-display"><?php echo wc_price($expense->amount); ?></span>
                                    </td>
                                    <td class="wcm-col-category">
                                        <span class="wcm-category-badge wcm-category-<?php echo sanitize_html_class(strtolower($expense->category)); ?>">
                                            <?php echo esc_html($expense->category); ?>
                                        </span>
                                    </td>
                                    <td class="wcm-col-date">
                                        <div class="wcm-date-info">
                                            <span class="wcm-expense-date"><?php echo date_i18n(get_option('date_format'), strtotime($expense->expense_date)); ?></span>
                                            <div class="wcm-created-date">
                                                <span class="dashicons dashicons-clock"></span>
                                                <?php printf(__('Added: %s', 'woo-cash-manager'), date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($expense->created_at))); ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="wcm-col-actions">
                                        <div class="wcm-actions-dropdown">
                                            <button type="button" class="wcm-actions-toggle" title="<?php _e('More Actions', 'woo-cash-manager'); ?>">
                                                <span class="dashicons dashicons-ellipsis"></span>
                                            </button>
                                            <div class="wcm-actions-menu">
                                                <button type="button"
                                                        class="wcm-action-item wcm-edit-expense-btn"
                                                        data-expense-id="<?php echo $expense->id; ?>"
                                                        data-expense-title="<?php echo esc_attr($expense->title); ?>"
                                                        data-expense-amount="<?php echo esc_attr($expense->amount); ?>"
                                                        data-expense-category="<?php echo esc_attr($expense->category); ?>"
                                                        data-expense-note="<?php echo esc_attr($expense->note); ?>"
                                                        data-expense-date="<?php echo esc_attr($expense->expense_date); ?>"
                                                        title="<?php _e('Edit Expense', 'woo-cash-manager'); ?>">
                                                    <span class="dashicons dashicons-edit"></span>
                                                    <?php _e('Edit', 'woo-cash-manager'); ?>
                                                </button>
                                                <button type="button"
                                                        class="wcm-action-item wcm-duplicate-expense-btn"
                                                        data-expense-id="<?php echo $expense->id; ?>"
                                                        data-expense-title="<?php echo esc_attr($expense->title); ?>"
                                                        data-expense-amount="<?php echo esc_attr($expense->amount); ?>"
                                                        data-expense-category="<?php echo esc_attr($expense->category); ?>"
                                                        data-expense-note="<?php echo esc_attr($expense->note); ?>"
                                                        title="<?php _e('Duplicate Expense', 'woo-cash-manager'); ?>">
                                                    <span class="dashicons dashicons-admin-page"></span>
                                                    <?php _e('Duplicate', 'woo-cash-manager'); ?>
                                                </button>
                                                <button type="button"
                                                        class="wcm-action-item wcm-delete-btn"
                                                        data-expense-id="<?php echo $expense->id; ?>"
                                                        title="<?php _e('Delete Expense', 'woo-cash-manager'); ?>">
                                                    <span class="dashicons dashicons-trash"></span>
                                                    <?php _e('Delete', 'woo-cash-manager'); ?>
                                                </button>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="wcm-pagination">
                    <?php
                    $pagination_args = array(
                        'base' => add_query_arg('paged', '%#%'),
                        'format' => '',
                        'prev_text' => __('&laquo; Previous', 'woo-cash-manager'),
                        'next_text' => __('Next &raquo;', 'woo-cash-manager'),
                        'total' => $total_pages,
                        'current' => $current_page,
                        'show_all' => false,
                        'end_size' => 1,
                        'mid_size' => 2,
                        'type' => 'plain',
                    );

                    echo paginate_links($pagination_args);
                    ?>
                </div>
            <?php endif; ?>

        <?php else: ?>
            <div class="wcm-no-data">
                <div class="wcm-no-data-icon">
                    <span class="dashicons dashicons-clipboard"></span>
                </div>
                <h3><?php _e('No expenses found', 'woo-cash-manager'); ?></h3>
                <p><?php _e('You haven\'t recorded any expenses yet. Start by adding your first expense!', 'woo-cash-manager'); ?></p>
                <div class="wcm-no-data-actions">
                    <button type="button" class="button button-primary wcm-add-expense-trigger">
                        <?php _e('Add Your First Expense', 'woo-cash-manager'); ?>
                    </button>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add/Edit Expense Modal -->
<div id="wcm-expense-modal" class="wcm-modal" style="display: none;">
    <div class="wcm-modal-content wcm-expense-modal-content">
        <div class="wcm-modal-header">
            <h3 id="wcm-modal-title"><?php _e('Add New Expense', 'woo-cash-manager'); ?></h3>
            <span class="wcm-modal-close">&times;</span>
        </div>
        <div class="wcm-modal-body">
            <form id="wcm-expense-form" method="post" action="">
                <?php wp_nonce_field('wcm_add_expense', 'wcm_nonce'); ?>
                <input type="hidden" id="wcm-expense-id" name="expense_id" value="">
                <input type="hidden" id="wcm-form-action" name="wcm_add_expense" value="1">

                <div class="wcm-form-grid">
                    <!-- Title Field -->
                    <div class="wcm-form-group wcm-form-group-full">
                        <label for="wcm-modal-title-field" class="wcm-label">
                            <?php _e('Expense Title', 'woo-cash-manager'); ?>
                            <span class="wcm-required">*</span>
                        </label>
                        <input type="text"
                               id="wcm-modal-title-field"
                               name="title"
                               class="wcm-input"
                               placeholder="<?php _e('e.g., Office supplies, Marketing campaign, etc.', 'woo-cash-manager'); ?>"
                               required>
                    </div>

                    <!-- Amount and Category Row -->
                    <div class="wcm-form-group">
                        <label for="wcm-modal-amount" class="wcm-label">
                            <?php _e('Amount', 'woo-cash-manager'); ?>
                            <span class="wcm-required">*</span>
                        </label>
                        <div class="wcm-input-wrapper">
                            <span class="wcm-currency-symbol"><?php echo get_woocommerce_currency_symbol(); ?></span>
                            <input type="number"
                                   id="wcm-modal-amount"
                                   name="amount"
                                   class="wcm-input wcm-amount-input"
                                   step="0.01"
                                   min="0.01"
                                   placeholder="0.00"
                                   required>
                        </div>
                    </div>

                    <div class="wcm-form-group">
                        <label for="wcm-modal-category" class="wcm-label">
                            <?php _e('Category', 'woo-cash-manager'); ?>
                            <span class="wcm-required">*</span>
                        </label>
                        <input type="text"
                               id="wcm-modal-category"
                               name="category"
                               class="wcm-input"
                               placeholder="<?php _e('e.g., Marketing, Office, Travel', 'woo-cash-manager'); ?>"
                               list="wcm-modal-category-suggestions"
                               required>

                        <?php if (!empty($common_categories)): ?>
                            <datalist id="wcm-modal-category-suggestions">
                                <?php foreach ($common_categories as $category): ?>
                                    <option value="<?php echo esc_attr($category); ?>">
                                <?php endforeach; ?>
                            </datalist>
                        <?php endif; ?>
                    </div>

                    <!-- Date Field -->
                    <div class="wcm-form-group">
                        <label for="wcm-modal-date" class="wcm-label">
                            <?php _e('Expense Date', 'woo-cash-manager'); ?>
                            <span class="wcm-required">*</span>
                        </label>
                        <input type="date"
                               id="wcm-modal-date"
                               name="expense_date"
                               class="wcm-input"
                               value="<?php echo date('Y-m-d'); ?>"
                               max="<?php echo date('Y-m-d'); ?>"
                               required>
                    </div>

                    <!-- Note Field -->
                    <div class="wcm-form-group wcm-form-group-full">
                        <label for="wcm-modal-note" class="wcm-label">
                            <?php _e('Note (Optional)', 'woo-cash-manager'); ?>
                        </label>
                        <textarea id="wcm-modal-note"
                                  name="note"
                                  class="wcm-textarea"
                                  rows="3"
                                  placeholder="<?php _e('Add any additional details about this expense...', 'woo-cash-manager'); ?>"></textarea>
                    </div>
                </div>

                <!-- Quick Categories -->
                <?php if (!empty($common_categories)): ?>
                    <div class="wcm-modal-quick-categories">
                        <label class="wcm-label"><?php _e('Quick Categories:', 'woo-cash-manager'); ?></label>
                        <div class="wcm-category-buttons">
                            <?php foreach (array_slice($common_categories, 0, 8) as $category): ?>
                                <button type="button"
                                        class="button wcm-category-btn"
                                        data-category="<?php echo esc_attr($category); ?>">
                                    <?php echo esc_html($category); ?>
                                </button>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </form>
        </div>
        <div class="wcm-modal-footer">
            <button type="button" class="button" id="wcm-modal-cancel"><?php _e('Cancel', 'woo-cash-manager'); ?></button>
            <button type="submit" form="wcm-expense-form" class="button button-primary" id="wcm-modal-save">
                <span class="dashicons dashicons-plus-alt"></span>
                <?php _e('Add Expense', 'woo-cash-manager'); ?>
            </button>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="wcm-delete-modal" class="wcm-modal" style="display: none;">
    <div class="wcm-modal-content">
        <div class="wcm-modal-header">
            <h3><?php _e('Confirm Deletion', 'woo-cash-manager'); ?></h3>
            <span class="wcm-modal-close">&times;</span>
        </div>
        <div class="wcm-modal-body">
            <p><?php _e('Are you sure you want to delete this expense? This action cannot be undone.', 'woo-cash-manager'); ?></p>
        </div>
        <div class="wcm-modal-footer">
            <button type="button" class="button" id="wcm-cancel-delete"><?php _e('Cancel', 'woo-cash-manager'); ?></button>
            <button type="button" class="button button-primary" id="wcm-confirm-delete"><?php _e('Delete', 'woo-cash-manager'); ?></button>
        </div>
    </div>
</div>

<script type="text/javascript">
// Initialize chart data
var wcmChartData = <?php echo json_encode($monthly_data); ?>;
</script>
