<?php
/**
 * Uninstall script for Woo Cash Manager
 * 
 * This file is executed when the plugin is deleted from the WordPress admin.
 * It removes all plugin data including database tables and options.
 */

// Prevent direct access
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

// Define constants
define('WCM_TABLE_NAME', 'wc_cash_expenses');

/**
 * Remove plugin data
 */
function wcm_uninstall_plugin() {
    global $wpdb;
    
    // Remove database table
    $table_name = $wpdb->prefix . WCM_TABLE_NAME;
    $wpdb->query("DROP TABLE IF EXISTS $table_name");
    
    // Remove plugin options
    delete_option('wcm_plugin_version');
    delete_option('wcm_settings');
    
    // Remove any transients
    delete_transient('wcm_dashboard_data');
    delete_transient('wcm_chart_data');
    
    // Remove user meta related to plugin
    $wpdb->query("DELETE FROM {$wpdb->usermeta} WHERE meta_key LIKE 'wcm_%'");
    
    // Clear any cached data
    wp_cache_flush();
}

// Execute uninstall
wcm_uninstall_plugin();
